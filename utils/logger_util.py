"""
日志工具模块

提供统一的日志记录功能，用于替换项目中的print语句。

使用示例:
    from utils.logger_util import logger

    logger.info("这是一条信息日志")
    logger.error("这是一条错误日志")
    logger.debug("调试信息")
"""

import logging
import sys
from pathlib import Path
from datetime import datetime


def _setup_logger():
    """设置全局logger"""
    # 创建logger
    logger = logging.getLogger("data_profiling")
    logger.setLevel(logging.INFO)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)

    # 控制台格式
    console_format = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(console_format)

    # 文件处理器
    log_dir = Path("logs")
    if not log_dir.exists():
        log_dir.mkdir(exist_ok=True)

    file_handler = logging.FileHandler(
        log_dir / f"data_profiling_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)

    # 文件格式
    file_format = logging.Formatter(
        '%(asctime)s | %(levelname)s | %(funcName)s:%(lineno)d | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(file_format)

    # 添加处理器
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger


# 全局logger实例，供其他脚本直接使用
logger = _setup_logger()