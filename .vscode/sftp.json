{
    "name": "My Server",                                          // 服务器连接名称，用于在VS Code中识别此配置
    "host": "127.0.0.1",                                         // 远程服务器的IP地址或域名
    "protocol": "sftp",                                           // 传输协议，支持sftp、ftp、ftps等
    "port": 6000,                                                 // 服务器端口号
    "username": "rukbat",                                         // 登录用户名
    "remotePath": "/matrix/0-Work/0_dev/data_profiling",          // 远程服务器上的目标目录路径
    "uploadOnSave": true,                                         // 保存文件时自动上传到远程服务器
    "useTempFile": false,                                         // 是否使用临时文件进行传输
    "openSsh": false,                                             // 是否使用OpenSSH配置
    "downloadOnOpen": true,                                       // 打开文件时自动从远程服务器下载最新版本
    "syncMode": "update",                                         // 同步模式：update(更新)、full(完全同步)
    "watcher": {                                                  // 文件监视器配置
        "files": "**/*",                                          // 监视的文件模式，**/* 表示所有文件
        "autoUpload": true,                                       // 文件变化时自动上传
        "autoDelete": true                                        // 本地删除文件时自动删除远程文件
    },
    "ignore": [                                                   // 忽略上传的文件和目录列表
        "**/.vscode/**",                                          // VS Code配置目录
        "**/.git/**",                                             // Git版本控制目录
        "**/node_modules/**",                                     // Node.js依赖包目录
        "**/.DS_Store",                                           // macOS系统文件
        "**/Thumbs.db",                                           // Windows缩略图缓存文件
        "**/*.tmp",                                               // 临时文件
        "**/*.log",                                               // 日志文件
        "**/ui-demo/**"                                           // 自定义忽略的ui-demo目录
    ],
    "remoteExplorer": {                                           // 远程资源管理器配置
        "filesExclude": [                                         // 在远程资源管理器中隐藏的文件
            "**/ui-demo/**"                                       // 隐藏ui-demo目录
        ]
    },
    "syncOption": {                                               // 同步选项配置
        "delete": true,                                           // 允许删除远程文件
        "skipCreate": false,                                      // 是否跳过创建新文件
        "ignoreExisting": false,                                  // 是否忽略已存在的文件
        "update": true                                            // 是否更新已存在的文件
    }
}