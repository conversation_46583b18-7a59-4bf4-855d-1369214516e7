"""
资源文件加载工具模块
提供统一的字典和资源文件加载功能
"""

import json
from typing import Dict, Any, Set
from pathlib import Path
from utils.logger_util import logger


def load_words_asset(words_dir: str, words_type: str) -> Dict[str, Any]:
    """
    加载字典资源文件的统一方法

    Args:
        words_dir: 字典文件所在目录路径（相对于profiling/assets）
        words_type: 字典类型，支持以下类型：
            - 'stopwords': 停用词字典，返回包含'zh'和'en'键的字典
            - 'sensitive_words': 敏感词字典，返回敏感词及其信息的字典
            - 'custom': 自定义字典，直接返回JSON内容

    Returns:
        根据words_type返回不同格式的数据：
        - stopwords: Dict[str, List[str]] - {'zh': [...], 'en': [...]}
        - sensitive_words: Dict[str, Dict[str, str]] - {word: {type, level}}
        - custom: 原始JSON数据

    Raises:
        FileNotFoundError: 当指定的字典文件不存在时
        json.JSONDecodeError: 当JSON文件格式错误时
        ValueError: 当words_type不支持时
    """
    # 获取profiling包的根目录
    current_file = Path(__file__).resolve()
    profiling_root = current_file.parent.parent  # 从utils回到profiling目录

    # 构建assets目录路径
    assets_dir = profiling_root / 'assets'

    # 构建完整的字典文件路径
    if words_dir:
        dict_path = assets_dir / words_dir
    else:
        dict_path = assets_dir

    # 根据字典类型确定文件名
    if words_type == 'stopwords':
        file_path = dict_path / 'stopwords.json'
    elif words_type == 'sensitive_words':
        file_path = dict_path / 'sensitive_words.json'
    else:
        # 对于自定义类型，words_type应该是文件名
        file_path = dict_path / f'{words_type}.json'

    # 检查文件是否存在
    if not file_path.exists():
        raise FileNotFoundError(f"字典文件不存在: {file_path}")

    # 加载JSON文件
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"JSON文件格式错误 {file_path}: {e}", e.doc, e.pos)

    # 根据字典类型进行数据处理和验证
    if words_type == 'stopwords':
        return _process_stopwords(data, file_path)
    elif words_type == 'sensitive_words':
        return _process_sensitive_words(data, file_path)
    else:
        # 自定义类型直接返回原始数据
        return data


def _process_stopwords(data: Dict[str, Any], file_path: Path) -> Dict[str, Set[str]]:
    """
    处理停用词数据，确保格式正确

    Args:
        data: 从JSON文件加载的原始数据
        file_path: 文件路径，用于错误信息

    Returns:
        Dict[str, Set[str]]: 处理后的停用词字典，键为语言代码，值为停用词集合
    """
    if not isinstance(data, dict):
        raise ValueError(f"停用词文件格式错误 {file_path}: 根对象必须是字典")

    processed_data = {}

    # 处理中文停用词
    zh_words = data.get('zh', [])
    if not isinstance(zh_words, list):
        raise ValueError(f"停用词文件格式错误 {file_path}: 'zh'字段必须是列表")
    processed_data['zh'] = set(zh_words)

    # 处理英文停用词
    en_words = data.get('en', [])
    if not isinstance(en_words, list):
        raise ValueError(f"停用词文件格式错误 {file_path}: 'en'字段必须是列表")
    processed_data['en'] = set(en_words)

    return processed_data


def _process_sensitive_words(data: Dict[str, Any], file_path: Path) -> Dict[str, Dict[str, str]]:
    """
    处理敏感词数据，确保格式正确

    Args:
        data: 从JSON文件加载的原始数据
        file_path: 文件路径，用于错误信息

    Returns:
        Dict[str, Dict[str, str]]: 处理后的敏感词字典
    """
    if not isinstance(data, dict):
        raise ValueError(f"敏感词文件格式错误 {file_path}: 根对象必须是字典")

    processed_data = {}

    for word, word_info in data.items():
        if not isinstance(word_info, dict):
            raise ValueError(f"敏感词文件格式错误 {file_path}: 词'{word}'的信息必须是字典")

        # 确保必要的字段存在
        processed_info = {
            'sensitive_type': word_info.get('sensitive_type', '未知'),
            'sensitive_level': word_info.get('sensitive_level', '未知')
        }
        processed_data[word] = processed_info

    return processed_data


def get_default_stopwords() -> Dict[str, Set[str]]:
    """
    获取默认的停用词集合，用于文件加载失败时的备用方案

    Returns:
        Dict[str, Set[str]]: 默认停用词字典
    """
    return {
        'zh': {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '上', '也', '很', '到'},
        'en': {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
    }


def get_default_sensitive_words() -> Dict[str, Dict[str, str]]:
    """
    获取默认的敏感词字典，用于文件加载失败时的备用方案

    Returns:
        Dict[str, Dict[str, str]]: 默认敏感词字典
    """
    return {
        '测试敏感词1': {'sensitive_type': '测试', 'sensitive_level': '低'},
        '测试敏感词2': {'sensitive_type': '测试', 'sensitive_level': '低'}
    }


def load_operators_asset() -> Dict[str, Any]:
    """
    加载算子配置资源文件的统一方法

    从assets/operators.json文件中加载算子配置信息

    Returns:
        Dict[str, Any]: 算子配置字典，包含'operators'字段

    Raises:
        FileNotFoundError: 当算子配置文件不存在时
        json.JSONDecodeError: 当JSON文件格式错误时
        ValueError: 当配置文件格式不正确时
    """
    # 获取profiling包的根目录
    current_file = Path(__file__).resolve()
    profiling_root = current_file.parent.parent  # 从utils回到profiling目录

    # 构建assets目录路径
    assets_dir = profiling_root / 'assets'

    # 构建算子配置文件路径
    operators_file_path = assets_dir / 'operators.json'

    # 检查文件是否存在
    if not operators_file_path.exists():
        raise FileNotFoundError(f"算子配置文件不存在: {operators_file_path}")

    # 加载JSON文件
    try:
        with open(operators_file_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except json.JSONDecodeError as e:
        raise json.JSONDecodeError(f"算子配置文件JSON格式错误 {operators_file_path}: {e}", e.doc, e.pos)

    # 验证配置文件格式
    if not isinstance(config, dict):
        raise ValueError(f"算子配置文件格式错误 {operators_file_path}: 根对象必须是字典")

    if 'operators' not in config:
        raise ValueError(f"算子配置文件格式错误 {operators_file_path}: 缺少 'operators' 字段")

    if not isinstance(config['operators'], list):
        raise ValueError(f"算子配置文件格式错误 {operators_file_path}: 'operators' 字段必须是列表")

    # 验证每个算子的基本结构
    for i, operator in enumerate(config['operators']):
        if not isinstance(operator, dict):
            raise ValueError(f"算子配置文件格式错误 {operators_file_path}: operators[{i}] 必须是字典")

        # 检查必需字段
        required_fields = ['id', 'operator_name', 'operator_zh_name', 'desc']
        for field in required_fields:
            if field not in operator:
                raise ValueError(f"算子配置文件格式错误 {operators_file_path}: operators[{i}] 缺少必需字段 '{field}'")

    operators_count = len(config['operators'])
    logger.info(f"[asset_utils] 任务（未指定）：✅ 成功加载算子配置，共{operators_count}个算子")

    return config


def get_default_operators_config() -> Dict[str, Any]:
    """
    获取默认的算子配置，用于文件加载失败时的备用方案

    Returns:
        Dict[str, Any]: 默认算子配置字典
    """
    return {
        "operators": [
            {
                "id": "remove_special_chars",
                "operator_zh_name": "移除特殊字符",
                "operator_name": "remove_special_chars",
                "desc": "移除文本中的特殊字符、HTML实体、控制字符等",
                "parameters": [
                    {
                        "name": "keep_punctuation",
                        "type": "boolean",
                        "defaultValue": True,
                        "desc": "是否保留标点符号"
                    }
                ]
            },
            {
                "id": "normalize_whitespace",
                "operator_zh_name": "标准化空白字符",
                "operator_name": "normalize_whitespace",
                "desc": "统一空格、制表符、换行符等空白字符",
                "parameters": [
                    {
                        "name": "replace_tabs",
                        "type": "boolean",
                        "defaultValue": True,
                        "desc": "是否将制表符替换为空格"
                    }
                ]
            }
        ]
    }