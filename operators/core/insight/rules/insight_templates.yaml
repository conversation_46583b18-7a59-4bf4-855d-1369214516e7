# 数据洞察规则模板配置
# 统一配置文件，支持箱图统计指标和异常检测指标的洞察分析

# 字段类型说明：
# - quality_anomaly: 质量异常类，期望值为0或接近0，只关心异常检测
# - quality_ratio: 质量比例类，期望值接近1.0，关心偏离程度
# - content_count: 内容计数类，反映内容量，关心分布特征和异常值
# - structure_quality: 结构质量类，有明确正常范围，关心质量偏离
# - diversity_metrics: 多样性指标类，有正常范围，需要全面分布分析
# - anomaly_detection: 异常检测类，有值即异常，关心样本占比和异常程度
#
# 分析维度说明：
# - outliers: 异常值分析
# - median: 中位数偏向分析（仅对有normal_range的字段有效）
# - cv: 变异系数分析
# - skewness: 偏斜度分析
# - anomaly: 异常检测分析（仅对anomaly_detection类型有效）

# 箱图统计指标基础信息
boxplot_field_metadata:
  # 基础字符统计指标
  char_count:
    name: "字符数"
    meaning: "反映样本信息密度和完整性"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  alpha_ratio:
    name: "字母占比"
    meaning: "反映文本中字母字符的占比分布"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  digit_ratio:
    name: "数字占比"
    meaning: "反映文本中数值信息的占比密度"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  alnum_ratio:
    name: "字母数字占比"
    meaning: "反映文本中字母数字字符的总体占比"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  valid_ratio:
    name: "有效字符比例"
    meaning: "反映文本清洁度和噪声水平"
    normal_range: [0.85, null]  # 只关心下限，上限越高越好
    analysis_dimensions: ["median"]

  # 词汇统计指标
  word_count:
    name: "词数"
    meaning: "反映样本信息量和内容丰富度"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  stopword_count:
    name: "停用词数"
    meaning: "反映文本中功能词的使用频率"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  avg_word_length:
    name: "平均词长度"
    meaning: "反映词分割合理性和文本规范性"
    normal_range: [3.0, 10.0]
    analysis_dimensions: ["median", "cv"]

  # 结构统计指标
  sentence_count:
    name: "句子数"
    meaning: "反映样本结构完整性和语义连贯性"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  line_count:
    name: "行数"
    meaning: "反映文本格式结构和排版方式"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  avg_sentence_length:
    name: "平均句子长度"
    meaning: "反映句子结构复杂度和断句质量"
    normal_range: [10.0, 30.0]
    analysis_dimensions: ["median", "cv"]

  avg_line_length:
    name: "平均行长度"
    meaning: "反映文本格式规范性和排版质量"
    normal_range: null
    analysis_dimensions: ["outliers", "cv", "skewness"]

  # 重复度和多样性指标
  bigram_repetition:
    name: "2-gram重复度"
    meaning: "反映文本局部重复程度和内容多样性"
    normal_range: [null, 0.4]  # 只关心上限，下限越低越好（多样性高）
    analysis_dimensions: ["median", "cv", "skewness"]

  trigram_repetition:
    name: "3-gram重复度"
    meaning: "反映文本短语重复程度和表达多样性"
    normal_range: [null, 0.3]  # 只关心上限，下限越低越好（多样性高）
    analysis_dimensions: ["median", "cv", "skewness"]

  mtld_score:
    name: "MTLD词汇多样性"
    meaning: "反映文本词汇丰富度和表达复杂性"
    normal_range: [30.0, null]  # 只关心下限，上限越高越好（词汇丰富）
    analysis_dimensions: ["median", "cv", "skewness"]

# 异常检测指标基础信息
anomaly_field_metadata:
  special_ratio:
    name: "特殊字符异常"
    meaning: "特殊字符占比异常，有值即表示存在问题"
    analysis_dimensions: ["anomaly"]

  non_standard_spaces_ratio:
    name: "非标准空格异常"
    meaning: "非标准空格占比异常，表明编码或格式问题"
    analysis_dimensions: ["anomaly"]

  control_chars_ratio:
    name: "控制字符异常"
    meaning: "控制字符占比异常，可能影响文本处理"
    analysis_dimensions: ["anomaly"]

  format_chars_ratio:
    name: "格式字符异常"
    meaning: "格式字符占比异常，可能影响文本显示"
    analysis_dimensions: ["anomaly"]

# 统计阈值配置
thresholds:
  # 异常值比例阈值
  outlier_ratio:
    moderate: 0.10
    high: 0.20
    severe: 0.30

  # 变异系数阈值
  cv:
    low: 0.10
    moderate: 0.50
    high: 1.00
    very_high: 1.50

  # 偏斜度阈值
  skewness:
    moderate: 0.20
    high: 0.50
    severe: 1.00

  # IQR相对范围阈值
  iqr_ratio:
    narrow: 0.05
    wide: 2.0

  # 中位数偏向阈值（相对于正常范围的比例）
  median_bias:
    low_threshold: 0.25  # 左侧25%
    high_threshold: 0.75  # 右侧75%

  # 异常检测阈值
  anomaly_detection:
    # 异常样本占比阈值
    sample_proportion:
      low: 0.05      # 5%以下为轻微问题
      moderate: 0.15 # 15%以下为中等问题
      high: 0.30     # 30%以下为严重问题
      # 30%以上为极严重问题

    # 异常比率阈值（问题样本中的异常程度）
    anomaly_ratio:
      low: 0.01      # 1%以下为轻度异常
      moderate: 0.05 # 5%以下为中度异常
      high: 0.15     # 15%以下为重度异常
      # 15%以上为极重度异常

# 洞察模板配置
templates:
  # 异常值分析模板
  outliers:
    # 基于正常范围参考的洞察
    normal_range_based:
      below_range:
        default: "有值低于正常范围下限{min_normal:.2f}，需关注"
        avg_word_length: "有平均词长度低于正常范围下限{min_normal}的样本，可能存在过度分割问题或大量无意义单字符"
        avg_sentence_length: "有句子长度低于正常范围下限{min_normal}词的样本，可能存在过度断句或句子结构过于简单"
        valid_ratio: "存在有效字符低于{min_normal:.1%}的情况"
        mtld_score: "有词汇多样性低于{min_normal}的样本"

      above_range:
        default: "有值超过正常范围上限{max_normal:.2f}，需关注"
        avg_word_length: "有平均词长度超过正常范围上限{max_normal}字符的样本，可能存在分割失败或长字符串未处理"
        avg_sentence_length: "有句子长度超过正常范围上限{max_normal}词的样本，可能存在断句失败或长复合句"
        bigram_repetition: "有2-gram重复度超过{max_normal:.1%}的样本，存在严重重复内容"
        trigram_repetition: "有3-gram重复度超过{max_normal:.1%}的样本，短语重复度过高"
   
    # 基于IQR异常值分析的洞察
    iqr_based:
      lower:
        default: "有{count}个样本低于{lower_bound:.2f}"
        char_count: "有{count}个样本少于{lower_bound:.0f}字符"
        alpha_ratio: "有{count}个样本字母含量低于{lower_bound:.1%}"
        digit_ratio: "有{count}个样本数字含量低于{lower_bound:.1%}"
        alnum_ratio: "有{count}个样本数字字母含量低于{lower_bound:.1%}"
        word_count: "有{count}个样本少于{lower_bound:.0f}个词"
        stopword_count: "有{count}个样本停用词少于{lower_bound:.0f}个"
        avg_word_length: "有{count}个样本平均词长度低于{lower_bound:.1f}字符"
        sentence_count: "有{count}个样本句子数少于{lower_bound:.0f}句"
        line_count: "有{count}个样本行数少于{lower_bound:.0f}行"
        avg_sentence_length: "有{count}个样本平均句长度低于{lower_bound:.1f}词"
        avg_line_length: "有{count}个样本平均行长度低于{lower_bound:.1f}字符"
        valid_ratio: "有{count}个样本有效字符比例低于{lower_bound:.1%}"
        bigram_repetition: "有{count}个样本2-gram重复度低于{lower_bound:.1%}"
        trigram_repetition: "有{count}个样本3-gram重复度低于{lower_bound:.1%}"
        mtld_score: "有{count}个样本词汇多样性低于{lower_bound:.1f}"

      upper:
        default: "有{count}个样本超过{upper_bound:.2f}"
        char_count: "有{count}个样本超过{upper_bound:.0f}字符"
        alpha_ratio: "有{count}个样本字母含量超过{upper_bound:.1%}"
        digit_ratio: "有{count}个样本数字含量超过{upper_bound:.1%}"
        alnum_ratio: "有{count}个样本数字字母含量超过{upper_bound:.1%}"
        word_count: "有{count}个样本词数超过{upper_bound:.0f}词"
        stopword_count: "有{count}个样本停用词超过{upper_bound:.0f}个"
        avg_word_length: "有{count}个样本平均词长度超过{upper_bound:.1f}字符"
        sentence_count: "有{count}个样本句子数超过{upper_bound:.0f}句"
        line_count: "有{count}个样本行数超过{upper_bound:.0f}行"
        avg_sentence_length: "有{count}个样本平均句长度超过{upper_bound:.1f}词"
        avg_line_length: "有{count}个样本平均行长度超过{upper_bound:.1f}字符"
        valid_ratio: "有{count}个样本有效字符比例超过{upper_bound:.1%}"
        bigram_repetition: "有{count}个样本2-gram重复度超过{upper_bound:.1%}"
        trigram_repetition: "有{count}个样本3-gram重复度超过{upper_bound:.1%}"
        mtld_score: "有{count}个样本词汇多样性超过{upper_bound:.1f}"

  # 分布特征分析模板
  distribution:
    # 中位数分析
    median:
      below_range:
        default: "样本普遍低于正常范围"
        avg_word_length: "平均词长度普遍低于正常范围，可能存在过度分割或大量短词/单字符"
        avg_sentence_length: "句子长度普遍低于正常范围，可能存在过度断句或句子结构简单"
        valid_ratio: "有效字符普遍偏低，整体文本质量不佳，存在较多噪声"
        mtld_score: "词汇多样性普遍不足，可能是重复文本或词汇表达有限"

      above_range:
        default: "样本普遍超过正常范围"
        avg_word_length: "平均词长度普遍超过正常范围，可能存在分割失败或长字符串未处理"
        avg_sentence_length: "句子长度普遍超过正常范围，可能存在断句失败或长复合句过多"
        bigram_repetition: "2-gram重复度普遍超过正常范围，存在严重重复内容，可能是模板文本"
        trigram_repetition: "3-gram重复度普遍超过正常范围，短语重复度过高，可能是模板文本"

      high:
        default: "样本在正常范围内但普遍偏高"
        avg_word_length: "平均词长度在正常范围内但普遍偏高，可能存在部分分割问题或专业术语较多"
        avg_sentence_length: "句子长度在正常范围内但普遍偏高，可能存在部分断句不足或复合句较多"
        bigram_repetition: "2-gram重复度在正常范围内但普遍偏高，存在一定重复内容"
        trigram_repetition: "3-gram重复度在正常范围内但普遍偏高，短语重复度较高"

      low:
        default: "样本在正常范围内但普遍偏低"
        avg_word_length: "平均词长度在正常范围内但普遍偏低，可能存在部分过度分割"
        avg_sentence_length: "句子长度在正常范围内但普遍偏低，可能存在部分过度断句"
        valid_ratio: "有效字符在正常范围内但普遍偏低，文本质量有待提升"
        mtld_score: "词汇多样性在正常范围内但普遍偏低，表达丰富度有待提升"

    # IQR分析
    iqr:
      narrow:
        default: "数据分布过于集中，样本差异很小"
        char_count: "字符数分布过于集中，样本长度高度一致，可能是固定格式文本"
        word_count: "词数分布过于集中，样本信息量高度一致，可能是标准化内容"
        avg_word_length: "平均词长度分布过于集中，分词结果高度一致"
        avg_sentence_length: "平均句子长度分布过于集中，句子结构高度一致"
        alpha_ratio: "字母占比分布过于集中，字符构成高度一致"
        digit_ratio: "数字占比分布过于集中，数值信息密度高度一致"
        bigram_repetition: "2-gram重复度分布过于集中，重复模式高度一致"
        trigram_repetition: "3-gram重复度分布过于集中，短语重复模式高度一致"
        mtld_score: "词汇多样性分布过于集中，词汇丰富度高度一致"
      wide:
        default: "数据分布过于分散，样本差异很大"
        char_count: "字符数分布过于分散，样本长度差异极大，可能混合了不同类型文档"
        word_count: "词数分布过于分散，样本信息量差异极大，数据来源可能混乱"
        avg_word_length: "平均词长度分布过于分散，可能存在分词质量问题或多语言混合"
        avg_sentence_length: "平均句子长度分布过于分散，可能存在断句质量问题或文体混合"
        alpha_ratio: "字母占比分布过于分散，可能混合了不同语言类型的内容"
        digit_ratio: "数字占比分布过于分散，可能混合了数据表格和纯文本内容"
        bigram_repetition: "2-gram重复度分布过于分散，可能混合了原创和模板内容"
        trigram_repetition: "3-gram重复度分布过于分散，可能混合了不同风格的内容"
        mtld_score: "词汇多样性分布过于分散，可能混合了不同复杂度的内容"

    # 变异系数分析
    cv:
      low:
        default: "数据变化幅度很小，样本差异不大"
        char_count: "字符数变化幅度很小，样本长度高度一致"
        word_count: "词数变化幅度很小，样本信息量高度一致"
        avg_word_length: "平均词长度变化幅度很小，分词结果高度一致"
        avg_sentence_length: "平均句子长度变化幅度很小，句子结构高度一致"
        alpha_ratio: "字母占比变化幅度很小，字符构成高度一致"
        digit_ratio: "数字占比变化幅度很小，数值信息密度高度一致"
        bigram_repetition: "2-gram重复度变化幅度很小，重复模式高度一致"
        trigram_repetition: "3-gram重复度变化幅度很小，短语重复模式高度一致"
        mtld_score: "词汇多样性变化幅度很小，词汇丰富度高度一致"
      moderate:
        default: "数据变化幅度适中，样本分布相对合理"
      high:
        default: "数据变化幅度较大，样本差异明显"
        char_count: "字符数变化幅度较大，样本长度差异明显，可能混合了不同类型内容"
        word_count: "词数变化幅度较大，样本信息量差异明显，可能包含不同类型内容"
        avg_word_length: "平均词长度变化幅度较大，可能存在分词质量不一致或多语言混合"
        avg_sentence_length: "平均句子长度变化幅度较大，句子结构差异明显"
        alpha_ratio: "字母占比变化幅度较大，可能混合了中英文或不同语言内容"
        digit_ratio: "数字占比变化幅度较大，可能混合了数据表格和纯文本内容"
        bigram_repetition: "2-gram重复度变化幅度较大，可能混合了原创和模板内容"
        trigram_repetition: "3-gram重复度变化幅度较大，表达风格差异明显"
        mtld_score: "词汇多样性变化幅度较大，可能混合了专业文档和通俗内容"
      very_high:
        default: "数据变化幅度极大，样本差异悬殊"
        char_count: "字符数变化幅度极大，样本长度差异悬殊，可能混合了不同类型文档"
        word_count: "词数变化幅度极大，样本信息量差异悬殊，数据来源可能混乱"
        avg_word_length: "平均词长度变化幅度极大，可能存在严重的分词错误或数据污染"
        avg_sentence_length: "平均句子长度变化幅度极大，可能存在严重的断句错误或格式问题"
        alpha_ratio: "字母占比变化幅度极大，可能混合了完全不同的语言类型"
        digit_ratio: "数字占比变化幅度极大，可能混合了纯数字数据和纯文本内容"
        bigram_repetition: "2-gram重复度变化幅度极大，可能混合了高度重复和完全原创的内容"
        trigram_repetition: "3-gram重复度变化幅度极大，可能混合了模板文本和原创内容"
        mtld_score: "词汇多样性变化幅度极大，可能混合了专业术语密集和词汇简单的内容"

    # 偏斜度分析
    skewness:
      moderate:
        positive:
          default: "数据分布右偏，大部分样本值偏小，少数样本值较大"
          char_count: "字符数分布右偏，大部分样本较短，少数样本明显较长"
          word_count: "词数分布右偏，大部分样本信息量较少，少数样本内容丰富"
          avg_word_length: "平均词长度分布右偏，大部分词较短，少数长词拉高均值"
          avg_sentence_length: "平均句子长度分布右偏，大部分句子较短，少数长句拉高均值"
          alpha_ratio: "字母占比分布右偏，大部分样本字母较少，少数样本字母密集"
          digit_ratio: "数字占比分布右偏，大部分样本数字较少，少数样本数字密集"
          bigram_repetition: "2-gram重复度分布右偏，大部分样本重复度低，少数样本高度重复"
          trigram_repetition: "3-gram重复度分布右偏，大部分样本短语重复少，少数样本重复严重"
          mtld_score: "词汇多样性分布右偏，大部分样本词汇简单，少数样本词汇丰富"
        negative:
          default: "数据分布左偏，大部分样本值偏大，少数样本值较小"
          char_count: "字符数分布左偏，大部分样本较长，少数样本明显较短"
          word_count: "词数分布左偏，大部分样本信息量丰富，少数样本内容稀少"
          avg_word_length: "平均词长度分布左偏，大部分词较长，少数短词拉低均值"
          avg_sentence_length: "平均句子长度分布左偏，大部分句子较长，少数短句拉低均值"
          alpha_ratio: "字母占比分布左偏，大部分样本字母较多，少数样本字母稀少"
          digit_ratio: "数字占比分布左偏，大部分样本数字较多，少数样本数字稀少"
          bigram_repetition: "2-gram重复度分布左偏，大部分样本重复度高，少数样本重复度低"
          trigram_repetition: "3-gram重复度分布左偏，大部分样本短语重复多，少数样本重复少"
          mtld_score: "词汇多样性分布左偏，大部分样本词汇丰富，少数样本词汇简单"
      high:
        positive:
          default: "数据分布明显右偏，存在较多异常高值样本"
          char_count: "字符数分布明显右偏，存在较多超长样本，可能有错误拼接或未分段的长文档"
          word_count: "词数分布明显右偏，存在较多高词数样本，可能有合并文档或冗余内容"
          avg_word_length: "平均词长度分布明显右偏，存在较多长词样本，可能有分词失败"
          avg_line_length: "平均行长度分布明显右偏，存在较多长行样本，可能有分行失败"
          avg_sentence_length: "平均句子长度分布明显右偏，存在较多长句样本，可能有断句失败"
          alpha_ratio: "字母占比分布明显右偏，存在较多高字母占比样本，可能混合了大量英文内容"
          digit_ratio: "数字占比分布明显右偏，存在较多高数字占比样本，可能混合了数值数据"
          bigram_repetition: "2-gram重复度分布明显右偏，存在较多高重复样本，可能有大量模板内容"
          trigram_repetition: "3-gram重复度分布明显右偏，存在较多高重复样本，可能有固定表达或复制内容"
          mtld_score: "词汇多样性分布明显右偏，存在较多高多样性样本，可能混合了专业术语密集内容"
        negative:
          default: "数据分布明显左偏，存在较多异常低值样本"
          char_count: "字符数分布明显左偏，存在较多超短样本，可能有大量无效数据或空白内容"
          word_count: "词数分布明显左偏，存在较多低词数样本，可能有大量标题、标签或不完整文本"
          avg_word_length: "平均词长度分布明显左偏，存在较多短词样本，可能有过度分割"
          avg_sentence_length: "平均句子长度分布明显左偏，存在较多短句样本，可能有过度断句"
          alpha_ratio: "字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"
          digit_ratio: "数字占比分布明显左偏，存在较多低数字占比样本，可能主要是纯文本内容"
          bigram_repetition: "2-gram重复度分布明显左偏，存在较多低重复样本，可能有大量原创内容"
          trigram_repetition: "3-gram重复度分布明显左偏，存在较多低重复样本，内容可能过于分散"
          mtld_score: "词汇多样性分布明显左偏，存在较多低多样性样本，可能有大量重复内容"
      severe:
        positive:
          default: "数据分布极度右偏，存在极端高值样本严重拉高分布"
          char_count: "字符数分布极度右偏，存在极端长样本严重拉高分布，可能有严重的数据合并错误"
          word_count: "词数分布极度右偏，存在极端高词数样本严重拉高分布，数据来源可能严重混乱"
          avg_word_length: "平均词长度分布极度右偏，存在极端长词严重拉高分布，可能有严重的分词错误"
          avg_sentence_length: "平均句子长度分布极度右偏，存在极端长句严重拉高分布，可能有严重的断句失败"
          alpha_ratio: "字母占比分布极度右偏，存在极端高字母占比样本，可能混合了完全不同的语言类型"
          digit_ratio: "数字占比分布极度右偏，存在极端高数字占比样本，可能混合了纯数字数据"
          bigram_repetition: "2-gram重复度分布极度右偏，存在极端高重复样本，可能有严重的重复内容问题"
          trigram_repetition: "3-gram重复度分布极度右偏，存在极端高重复样本，可能有大量复制粘贴内容"
          mtld_score: "词汇多样性分布极度右偏，存在极端高多样性样本，可能混合了完全不同复杂度的内容"
        negative:
          default: "数据分布极度左偏，存在极端低值样本严重拉低分布"
          char_count: "字符数分布极度左偏，存在极端短样本严重拉低分布，可能有大量空白或无效数据"
          word_count: "词数分布极度左偏，存在极端低词数样本严重拉低分布，可能有大量无效数据"
          avg_word_length: "平均词长度分布极度左偏，存在极端短词严重拉低分布，可能有严重的过度分割问题"
          avg_sentence_length: "平均句子长度分布极度左偏，存在极端短句严重拉低分布，可能有严重的过度断句问题"
          alpha_ratio: "字母占比分布极度左偏，存在极端低字母占比样本，可能包含大量非字母内容"
          digit_ratio: "数字占比分布极度左偏，存在极端低数字占比样本，可能完全缺乏数值信息"
          bigram_repetition: "2-gram重复度分布极度左偏，存在极端低重复样本，可能有大量随机内容"
          trigram_repetition: "3-gram重复度分布极度左偏，存在极端低重复样本，内容可能过于分散"
          mtld_score: "词汇多样性分布极度左偏，存在极端低多样性样本，可能有大量重复内容"

  # 异常检测分析模板
  anomaly:
    # 轻微问题模板
    light:
      special_ratio: "{anomaly_sample_proportion:.1%}的样本包含特殊字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较轻微，基本不影响训练效果，建议适当清理特殊字符"
      non_standard_spaces_ratio: "{anomaly_sample_proportion:.1%}的样本包含非标准空格异常，异常空格占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较轻微，建议统一使用标准空格字符"
      control_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含控制字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较轻微，建议清理隐藏控制字符"
      format_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含格式字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较轻微，建议移除或转换格式字符"

    # 中等问题模板
    moderate:
      special_ratio: "{anomaly_sample_proportion:.1%}的样本包含特殊字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题程度中等，可能对训练产生一定影响，建议制定特殊字符清洗规则"
      non_standard_spaces_ratio: "{anomaly_sample_proportion:.1%}的样本包含非标准空格异常，异常空格占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题程度中等，建议检查编码设置，规范化空白字符"
      control_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含控制字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题程度中等，建议检查数据转换过程，移除控制字符"
      format_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含格式字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题程度中等，建议标准化文本格式，统一处理格式字符"

    # 严重问题模板
    severe:
      special_ratio: "{anomaly_sample_proportion:.1%}的样本包含特殊字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较严重，可能显著影响训练效果，建议深度清洗数据，移除HTML实体和非文本字符"
      non_standard_spaces_ratio: "{anomaly_sample_proportion:.1%}的样本包含非标准空格异常，异常空格占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较严重，建议进行编码转换，统一使用UTF-8编码"
      control_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含控制字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较严重，建议深度清洗，可能来自特定格式转换"
      format_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含格式字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题较严重，建议深度清洗格式字符，可能影响文本处理"

    # 极严重问题模板
    critical:
      special_ratio: "{anomaly_sample_proportion:.1%}的样本包含特殊字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题极严重，严重影响训练效果，建议重新审查数据来源，可能存在格式转换问题"
      non_standard_spaces_ratio: "{anomaly_sample_proportion:.1%}的样本包含非标准空格异常，异常空格占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题极严重，建议重新处理原始数据，可能存在严重编码问题"
      control_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含控制字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题极严重，建议重新获取原始数据，避免格式转换问题"
      format_chars_ratio: "{anomaly_sample_proportion:.1%}的样本包含格式字符异常，异常字符占比约{median_anomaly_ratio:.2%}，最高占比{max_anomaly_ratio:.2%}。问题极严重，建议重新处理原始数据，避免格式字符混入"

# 占比统计指标基础信息
proportion_field_metadata:
  language:
    name: "语言类型"
    meaning: "文本样本的语言分布情况"
    analysis_dimensions: ["distribution"]

  encoding:
    name: "编码类型"
    meaning: "文本样本的字符编码分布情况"
    analysis_dimensions: ["distribution"]

# 占比统计指标洞察模板
proportion_templates:
  # 单一类型模板
  single_type:
    language: "样本全部为{type}语言类型。"
    encoding: "样本全部使用{type}编码。"

  # 多类型模板
  multiple_types:
    language: "样本涉及{count}种语言如{types}，其中{dominant_type}占比{dominant_ratio:.1%}是比例最高的语言类型。"
    encoding: "样本涉及{count}种编码如{types}，其中{dominant_type}占比{dominant_ratio:.1%}是比例最高的编码类型。"

  # 多类型无主导模板（当没有most_common数据时）
  multiple_types_no_dominant:
    language: "样本涉及{count}种语言如{types}。"
    encoding: "样本涉及{count}种编码如{types}。"

