
"""
数据画像与洞察

"""

from typing import Dict, Any

from operators.ops import load_ops, DataProfiler
from operators.utils.llm_client import LLMClient
from operators.utils.asset_utils import load_operators_asset
from utils import es_client
from datasets import concatenate_datasets
from operators.core.insight import DataInsightCore
from operators.core.profiling import DatasetProfilerCore
from operators.core.orchestra import OperatorOrchestratorCore
from utils.logger_util import logger

class Insighter:
    """数据画像与洞察执行器"""

    def __init__(self, cfg: dict):
        """
        初始化编排器

        Args:
            cfg: 配置字典
        """
        self.cfg = cfg
        self.task_id = cfg.get('task_id')
        self.ops = None
        self.process_list = cfg.get('process_list')
        self.callback = cfg.get('callback')
        self.data_type = cfg.get('data_type')
        self.dataset_batch_id = cfg.get('dataset_batch_id')
        
        # 加载算子配置
        self.operators_config = load_operators_asset()

        # 初始化LLM客户端，使用context中的配置
        self.llm_client = LLMClient()
        logger.info(f"[insighter] 任务（{self.task_id}）：LLM客户端初始化成功")
   
    def stratified_sampling(self, origin_total, sample_size):
        pass
    
    def transfer_data(self, dataset):
        return concatenate_datasets([dataset])
    
    def run_insight(self, op, origin_total):
        """运行算子编排流程"""
        origin_list = self.stratified_sampling(origin_total=origin_total, sample_size=1000)
        dataset = self.transfer_data(dataset=origin_list)
        clean_keys = op.clean_keys
        profiling_core = DatasetProfilerCore()
        insight_core = DataInsightCore()

        # 步骤1：指标特征统计
        dataset = dataset.map(
            function=op.process,
            num_proc=1,
            with_rank=False,
            desc=op.op_name
        )

        # 步骤2: 数据画像分析
        profiling_report = profiling_core.apply_profile(dataset, clean_keys)

        # 步骤3: 洞察分析
        insight_report = insight_core.apply_insights(profiling_report)
        
        # 步骤4: 上传到ES
        if self.dataset_batch_id:
            try:
                success = es_client.upload_profiling_data(self.dataset_batch_id, insight_report)
                if success:
                    logger.info(f"[insighter] 任务（{self.task_id}）：报告已成功上传到ES: {self.dataset_batch_id}")
                else:
                    logger.error(f"[insighter] 任务（{self.task_id}）：报告上传到ES失败: {self.dataset_batch_id}")
            except Exception as e:
                logger.error(f"[insighter] 任务（{self.task_id}）：上传报告到ES时发生错误: {e}")
        logger.info(f"[insighter] 任务（{self.task_id}）：数据洞察流程完成")

    def run(self):
        """
        完成算子编排的全部流程：数据画像 -> 洞察分析 ->  ES上传
        """
        self.process_list, self.ops = load_ops(self.process_list, False)
        # redis
        for op_cfg, op in zip(self.process_list, self.ops):
            op_name, op_args = list(op_cfg.items())[0]

            origin_total = es_client.count_batch_data()

            if isinstance(op, DataProfiler):
                self.run_insight(op=op, origin_total=origin_total)
            else:
                raise NotImplementedError
