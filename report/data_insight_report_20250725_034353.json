{"summary": {"total_samples": 50, "analysis_timestamp": "2025-07-25T03:43:56.850429", "analyzed_fields": ["system", "system_length", "prompt", "prompt_length", "response", "response_length"], "language_distribution": {"total_count": 50, "proportions": {"zh": {"count": 50, "proportion": 1.0}}, "most_common": ["zh", 50], "insight": "language字段(总样本50，1个类别)存在以下问题：language字段分布过度集中于'zh'类别(100.00%)建议：增加数据来源多样性。"}, "avg_text_length": 184.02, "content_diversity": 27.88, "domain_insights": "基于样本文本及模式匹配结果，该数据集主要涉及个人身份信息（PII）识别与敏感信息检测领域，可能用于数据隐私保护、文本脱敏及合规审查等任务训练。数据中邮箱、手机号码、身份证号、网址及HTML标签均有较好覆盖，尤其在response字段匹配比例较高，说明该字段信息含量丰富。敏感词汇检测规则覆盖面不足，所有字段均匹配到‘无’，提示敏感词库或匹配逻辑存在缺陷。建议优化敏感词库及检测规则，补充姓名等PII识别，增强system_length、prompt_length、response_length等字段的PII检测能力，以提升整体数据质量和模型训练效果。"}, "boxplot_stats": {"system": {"char_count": {"count": 50, "mean": 221.5, "std": 196.4609, "min": 0.0, "q1": 164.25, "median": 200.0, "q3": 200.0, "max": 874.0, "iqr": 35.75, "lower_bound": 110.625, "upper_bound": 253.625, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "字符数(反映样本信息密度和完整性)：有8个样本少于111字符；有5个样本超过254字符；数据变化幅度适中，样本分布相对合理；字符数分布右偏，大部分样本较短，少数样本明显较长"}, "alpha_ratio": {"count": 50, "mean": 0.7413, "std": 0.1968, "min": 0.0, "q1": 0.7216, "median": 0.7869, "q3": 0.8463, "max": 1.0, "iqr": 0.1247, "lower_bound": 0.5346, "upper_bound": 1.0332, "lower_outlier_count": 5, "upper_outlier_count": 0, "insight": "字母占比(反映文本中字母字符的占比分布)：有5个样本字母含量低于53.5%；字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 50, "mean": 0.0593, "std": 0.1049, "min": 0.0, "q1": 0.0, "median": 0.0344, "q3": 0.09, "max": 0.6923, "iqr": 0.09, "lower_bound": -0.135, "upper_bound": 0.225, "lower_outlier_count": 0, "upper_outlier_count": 1, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度极大，可能混合了纯数字数据和纯文本内容；数字占比分布明显右偏，存在较多高数字占比样本，可能混合了数值数据"}, "alnum_ratio": {"count": 50, "mean": 0.8005, "std": 0.1806, "min": 0.0, "q1": 0.81, "median": 0.8376, "q3": 0.8676, "max": 1.0, "iqr": 0.0576, "lower_bound": 0.7236, "upper_bound": 0.9539, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：有5个样本数字字母含量低于72.4%；有3个样本数字字母含量超过95.4%；数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 50, "mean": 0.8005, "std": 0.1806, "min": 0.0, "q1": 0.81, "median": 0.8376, "q3": 0.8676, "max": 1.0, "iqr": 0.0576, "lower_bound": 0.7236, "upper_bound": 0.9539, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "有效字符比例(反映文本清洁度和噪声水平)：有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 50, "mean": 120.96, "std": 107.6216, "min": 0.0, "q1": 89.0, "median": 106.0, "q3": 115.75, "max": 484.0, "iqr": 26.75, "lower_bound": 48.875, "upper_bound": 155.875, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "词数(反映样本信息量和内容丰富度)：有8个样本少于49个词；有5个样本词数超过156词；数据变化幅度适中，样本分布相对合理；词数分布右偏，大部分样本信息量较少，少数样本内容丰富"}, "stopword_count": {"count": 50, "mean": 29.56, "std": 28.0907, "min": 0.0, "q1": 20.0, "median": 25.5, "q3": 30.75, "max": 131.0, "iqr": 10.75, "lower_bound": 3.875, "upper_bound": 46.875, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "停用词数(反映文本中功能词的使用频率)：有8个样本停用词少于4个；有5个样本停用词超过47个；数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 11.88, "std": 10.5539, "min": 0.0, "q1": 8.0, "median": 11.0, "q3": 12.0, "max": 48.0, "iqr": 4.0, "lower_bound": 2.0, "upper_bound": 18.0, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "句子数(反映样本结构完整性和语义连贯性)：有8个样本句子数少于2句；有5个样本句子数超过18句；数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "line_count": {"count": 50, "mean": 2.32, "std": 1.9742, "min": 0.0, "q1": 1.0, "median": 2.0, "q3": 2.0, "max": 8.0, "iqr": 1.0, "lower_bound": -0.5, "upper_bound": 3.5, "lower_outlier_count": 0, "upper_outlier_count": 10, "insight": "行数(反映文本格式结构和排版方式)：有10个样本行数超过4行；数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "avg_sentence_length": {"count": 50, "mean": 18.9098, "std": 7.5777, "min": 0.0, "q1": 16.1875, "median": 18.1951, "q3": 21.2917, "max": 50.0, "iqr": 5.1042, "lower_bound": 8.5312, "upper_bound": 28.9479, "lower_outlier_count": 4, "upper_outlier_count": 3, "insight": ""}, "avg_line_length": {"count": 50, "mean": 112.4817, "std": 84.7006, "min": 0.0, "q1": 49.25, "median": 99.5, "q3": 162.75, "max": 505.0, "iqr": 113.5, "lower_bound": -121.0, "upper_bound": 333.0, "lower_outlier_count": 0, "upper_outlier_count": 1, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "bigram_repetition": {"count": 50, "mean": 0.2696, "std": 0.1565, "min": 0.0, "q1": 0.2034, "median": 0.2575, "q3": 0.3407, "max": 0.677, "iqr": 0.1372, "lower_bound": -0.0025, "upper_bound": 0.5465, "lower_outlier_count": 0, "upper_outlier_count": 4, "insight": "2-gram重复度(反映文本局部重复程度和内容多样性)：数据变化幅度适中，样本分布相对合理；2-gram重复度分布右偏，大部分样本重复度低，少数样本高度重复"}, "trigram_repetition": {"count": 50, "mean": 0.1628, "std": 0.1211, "min": 0.0, "q1": 0.0957, "median": 0.144, "q3": 0.2058, "max": 0.5207, "iqr": 0.1101, "lower_bound": -0.0694, "upper_bound": 0.3709, "lower_outlier_count": 0, "upper_outlier_count": 4, "insight": "3-gram重复度(反映文本短语重复程度和表达多样性)：数据变化幅度适中，样本分布相对合理；3-gram重复度分布右偏，大部分样本短语重复少，少数样本重复严重"}, "mtld_score": {"count": 42, "mean": 28.4466, "std": 5.893, "min": 17.4, "q1": 24.575, "median": 28.0714, "q3": 31.375, "max": 45.3333, "iqr": 6.8, "lower_bound": 14.375, "upper_bound": 41.575, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "MTLD词汇多样性(反映文本词汇丰富度和表达复杂性)：词汇多样性普遍不足，可能是重复文本或词汇表达有限"}}, "system_length": {"char_count": {"count": 50, "mean": 2.76, "std": 0.5851, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 3.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 8, "upper_outlier_count": 0, "insight": "字符数(反映样本信息密度和完整性)：有8个样本少于3字符；字符数分布极度左偏，存在极端短样本严重拉低分布，可能有大量空白或无效数据"}, "alpha_ratio": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "digit_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度很小，数值信息密度高度一致"}, "alnum_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大"}, "valid_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "word_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数变化幅度很小，样本信息量高度一致"}, "stopword_count": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据变化幅度很小，样本差异不大"}, "line_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 50, "mean": 2.76, "std": 0.5851, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 3.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 8, "upper_outlier_count": 0, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：句子长度普遍低于正常范围，可能存在过度断句或句子结构简单"}, "avg_line_length": {"count": 50, "mean": 2.76, "std": 0.5851, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 3.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 8, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：有8个样本平均行长度低于3.0字符；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "bigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}, "prompt": {"char_count": {"count": 50, "mean": 303.88, "std": 257.3629, "min": 0.0, "q1": 234.5, "median": 300.0, "q3": 300.0, "max": 1369.0, "iqr": 65.5, "lower_bound": 136.25, "upper_bound": 398.25, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "字符数(反映样本信息密度和完整性)：有9个样本少于136字符；有5个样本超过398字符；数据变化幅度适中，样本分布相对合理"}, "alpha_ratio": {"count": 50, "mean": 0.7389, "std": 0.2111, "min": 0.0, "q1": 0.7249, "median": 0.7891, "q3": 0.8178, "max": 1.0, "iqr": 0.0929, "lower_bound": 0.5854, "upper_bound": 0.9572, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母占比(反映文本中字母字符的占比分布)：有5个样本字母含量低于58.5%；有3个样本字母含量超过95.7%；字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 50, "mean": 0.055, "std": 0.0505, "min": 0.0, "q1": 0.0, "median": 0.0481, "q3": 0.0867, "max": 0.1767, "iqr": 0.0867, "lower_bound": -0.13, "upper_bound": 0.2167, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "数字占比(反映文本中数值信息的占比密度)：数据变化幅度适中，样本分布相对合理；数字占比分布右偏，大部分样本数字较少，少数样本数字密集"}, "alnum_ratio": {"count": 50, "mean": 0.7938, "std": 0.2133, "min": 0.0, "q1": 0.8238, "median": 0.8467, "q3": 0.8633, "max": 1.0, "iqr": 0.0395, "lower_bound": 0.7645, "upper_bound": 0.9226, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：有5个样本数字字母含量低于76.4%；有3个样本数字字母含量超过92.3%；数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 50, "mean": 0.7938, "std": 0.2133, "min": 0.0, "q1": 0.8238, "median": 0.8467, "q3": 0.8633, "max": 1.0, "iqr": 0.0395, "lower_bound": 0.7645, "upper_bound": 0.9226, "lower_outlier_count": 5, "upper_outlier_count": 3, "insight": "有效字符比例(反映文本清洁度和噪声水平)：有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 50, "mean": 163.58, "std": 135.8499, "min": 0.0, "q1": 130.0, "median": 158.0, "q3": 168.0, "max": 722.0, "iqr": 38.0, "lower_bound": 73.0, "upper_bound": 225.0, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "词数(反映样本信息量和内容丰富度)：有9个样本少于73个词；有5个样本词数超过225词；数据变化幅度适中，样本分布相对合理"}, "stopword_count": {"count": 50, "mean": 41.22, "std": 34.0202, "min": 0.0, "q1": 29.25, "median": 38.5, "q3": 45.0, "max": 166.0, "iqr": 15.75, "lower_bound": 5.625, "upper_bound": 68.625, "lower_outlier_count": 8, "upper_outlier_count": 5, "insight": "停用词数(反映文本中功能词的使用频率)：有8个样本停用词少于6个；有5个样本停用词超过69个；数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 16.36, "std": 12.7354, "min": 0.0, "q1": 12.25, "median": 15.0, "q3": 18.0, "max": 62.0, "iqr": 5.75, "lower_bound": 3.625, "upper_bound": 26.625, "lower_outlier_count": 9, "upper_outlier_count": 5, "insight": "句子数(反映样本结构完整性和语义连贯性)：有9个样本句子数少于4句；有5个样本句子数超过27句；数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "line_count": {"count": 50, "mean": 2.3, "std": 2.0224, "min": 0.0, "q1": 1.0, "median": 1.0, "q3": 3.0, "max": 10.0, "iqr": 2.0, "lower_bound": -2.0, "upper_bound": 6.0, "lower_outlier_count": 0, "upper_outlier_count": 3, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度适中，样本分布相对合理；数据分布极度右偏，存在极端高值样本严重拉高分布"}, "avg_sentence_length": {"count": 50, "mean": 17.1764, "std": 6.3139, "min": 0.0, "q1": 15.9528, "median": 17.6312, "q3": 20.125, "max": 35.0, "iqr": 4.1722, "lower_bound": 9.6944, "upper_bound": 26.3833, "lower_outlier_count": 6, "upper_outlier_count": 2, "insight": ""}, "avg_line_length": {"count": 50, "mean": 157.4721, "std": 115.7779, "min": 0.0, "q1": 61.775, "median": 110.5833, "q3": 283.75, "max": 415.0, "iqr": 221.975, "lower_bound": -271.1875, "upper_bound": 616.7125, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据变化幅度适中，样本分布相对合理；数据分布极度右偏，存在极端高值样本严重拉高分布"}, "bigram_repetition": {"count": 50, "mean": 0.3419, "std": 0.1797, "min": 0.0, "q1": 0.2509, "median": 0.382, "q3": 0.4573, "max": 0.6893, "iqr": 0.2063, "lower_bound": -0.0586, "upper_bound": 0.7668, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "2-gram重复度(反映文本局部重复程度和内容多样性)：数据变化幅度适中，样本分布相对合理；2-gram重复度分布明显左偏，存在较多低重复样本，可能有大量原创内容"}, "trigram_repetition": {"count": 50, "mean": 0.2218, "std": 0.1382, "min": 0.0, "q1": 0.1312, "median": 0.2355, "q3": 0.2948, "max": 0.5319, "iqr": 0.1636, "lower_bound": -0.1141, "upper_bound": 0.5401, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "3-gram重复度(反映文本短语重复程度和表达多样性)：数据变化幅度适中，样本分布相对合理；3-gram重复度分布左偏，大部分样本短语重复多，少数样本重复少"}, "mtld_score": {"count": 41, "mean": 28.3798, "std": 6.2785, "min": 17.5, "q1": 25.1667, "median": 27.4286, "q3": 31.7143, "max": 43.6667, "iqr": 6.5476, "lower_bound": 15.3452, "upper_bound": 41.5357, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "MTLD词汇多样性(反映文本词汇丰富度和表达复杂性)：词汇多样性普遍不足，可能是重复文本或词汇表达有限；词汇多样性分布右偏，大部分样本词汇简单，少数样本词汇丰富"}}, "prompt_length": {"char_count": {"count": 50, "mean": 2.74, "std": 0.7158, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 2, "insight": "字符数(反映样本信息密度和完整性)：有9个样本少于3字符；有2个样本超过3字符；字符数分布极度左偏，存在极端短样本严重拉低分布，可能有大量空白或无效数据"}, "alpha_ratio": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "digit_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度很小，数值信息密度高度一致"}, "alnum_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大"}, "valid_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "word_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数变化幅度很小，样本信息量高度一致"}, "stopword_count": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据变化幅度很小，样本差异不大"}, "line_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 50, "mean": 2.74, "std": 0.7158, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 2, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：句子长度普遍低于正常范围，可能存在过度断句或句子结构简单"}, "avg_line_length": {"count": 50, "mean": 2.74, "std": 0.7158, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 2, "insight": "平均行长度(反映文本格式规范性和排版质量)：有9个样本平均行长度低于3.0字符；有2个样本平均行长度超过3.0字符；数据分布极度左偏，存在极端低值样本严重拉低分布"}, "bigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}, "response": {"char_count": {"count": 50, "mean": 570.36, "std": 570.9172, "min": 0.0, "q1": 368.5, "median": 476.0, "q3": 500.0, "max": 2476.0, "iqr": 131.5, "lower_bound": 171.25, "upper_bound": 697.25, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "字符数(反映样本信息密度和完整性)：有9个样本少于171字符；有7个样本超过697字符；字符数变化幅度较大，样本长度差异明显，可能混合了不同类型内容；字符数分布右偏，大部分样本较短，少数样本明显较长"}, "alpha_ratio": {"count": 50, "mean": 0.708, "std": 0.2094, "min": 0.0, "q1": 0.729, "median": 0.771, "q3": 0.8003, "max": 1.0, "iqr": 0.0712, "lower_bound": 0.6222, "upper_bound": 0.9071, "lower_outlier_count": 7, "upper_outlier_count": 1, "insight": "字母占比(反映文本中字母字符的占比分布)：有7个样本字母含量低于62.2%；有1个样本字母含量超过90.7%；字母占比分布明显左偏，存在较多低字母占比样本，可能主要是中文内容"}, "digit_ratio": {"count": 50, "mean": 0.0682, "std": 0.0893, "min": 0.0, "q1": 0.0279, "median": 0.0565, "q3": 0.0855, "max": 0.6098, "iqr": 0.0576, "lower_bound": -0.0586, "upper_bound": 0.1719, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度较大，可能混合了数据表格和纯文本内容；数字占比分布右偏，大部分样本数字较少，少数样本数字密集"}, "alnum_ratio": {"count": 50, "mean": 0.7762, "std": 0.2059, "min": 0.0, "q1": 0.8074, "median": 0.8307, "q3": 0.8537, "max": 1.0, "iqr": 0.0462, "lower_bound": 0.738, "upper_bound": 0.923, "lower_outlier_count": 6, "upper_outlier_count": 1, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：有6个样本数字字母含量低于73.8%；有1个样本数字字母含量超过92.3%；数据分布明显左偏，存在较多异常低值样本"}, "valid_ratio": {"count": 50, "mean": 0.7762, "std": 0.2059, "min": 0.0, "q1": 0.8074, "median": 0.8307, "q3": 0.8537, "max": 1.0, "iqr": 0.0462, "lower_bound": 0.738, "upper_bound": 0.923, "lower_outlier_count": 6, "upper_outlier_count": 1, "insight": "有效字符比例(反映文本清洁度和噪声水平)：有效字符普遍偏低，整体文本质量不佳，存在较多噪声"}, "word_count": {"count": 50, "mean": 309.3, "std": 307.4192, "min": 0.0, "q1": 199.25, "median": 251.0, "q3": 283.75, "max": 1331.0, "iqr": 84.5, "lower_bound": 72.5, "upper_bound": 410.5, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "词数(反映样本信息量和内容丰富度)：有9个样本少于72个词；有7个样本词数超过410词；数据变化幅度适中，样本分布相对合理；词数分布明显右偏，存在较多高词数样本，可能有合并文档或冗余内容"}, "stopword_count": {"count": 50, "mean": 76.86, "std": 78.3809, "min": 0.0, "q1": 43.75, "median": 63.0, "q3": 73.0, "max": 350.0, "iqr": 29.25, "lower_bound": -0.125, "upper_bound": 116.875, "lower_outlier_count": 0, "upper_outlier_count": 7, "insight": "停用词数(反映文本中功能词的使用频率)：有7个样本停用词超过117个；数据变化幅度较大，样本差异明显；数据分布明显右偏，存在较多异常高值样本"}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 29.42, "std": 29.792, "min": 0.0, "q1": 17.5, "median": 24.0, "q3": 27.75, "max": 133.0, "iqr": 10.25, "lower_bound": 2.125, "upper_bound": 43.125, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "句子数(反映样本结构完整性和语义连贯性)：有9个样本句子数少于2句；有7个样本句子数超过43句；数据变化幅度较大，样本差异明显；数据分布明显右偏，存在较多异常高值样本"}, "line_count": {"count": 50, "mean": 3.42, "std": 3.0139, "min": 0.0, "q1": 1.0, "median": 2.5, "q3": 4.75, "max": 13.0, "iqr": 3.75, "lower_bound": -4.625, "upper_bound": 10.375, "lower_outlier_count": 0, "upper_outlier_count": 3, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度适中，样本分布相对合理；数据分布明显右偏，存在较多异常高值样本"}, "avg_sentence_length": {"count": 50, "mean": 19.03, "std": 7.4134, "min": 0.0, "q1": 18.0932, "median": 19.1589, "q3": 20.4846, "max": 53.0, "iqr": 2.3915, "lower_bound": 14.506, "upper_bound": 24.0719, "lower_outlier_count": 5, "upper_outlier_count": 4, "insight": ""}, "avg_line_length": {"count": 50, "mean": 176.2604, "std": 133.4969, "min": 0.0, "q1": 95.1591, "median": 158.0, "q3": 243.675, "max": 518.0, "iqr": 148.5159, "lower_bound": -127.6148, "upper_bound": 466.4489, "lower_outlier_count": 0, "upper_outlier_count": 2, "insight": "平均行长度(反映文本格式规范性和排版质量)：数据变化幅度适中，样本分布相对合理；数据分布右偏，大部分样本值偏小，少数样本值较大"}, "bigram_repetition": {"count": 50, "mean": 0.4415, "std": 0.2127, "min": 0.0, "q1": 0.3945, "median": 0.4807, "q3": 0.5502, "max": 0.806, "iqr": 0.1557, "lower_bound": 0.1609, "upper_bound": 0.7837, "lower_outlier_count": 8, "upper_outlier_count": 1, "insight": "2-gram重复度(反映文本局部重复程度和内容多样性)：2-gram重复度普遍超过正常范围，存在严重重复内容，可能是模板文本；2-gram重复度分布明显左偏，存在较多低重复样本，可能有大量原创内容"}, "trigram_repetition": {"count": 50, "mean": 0.3077, "std": 0.1737, "min": 0.0, "q1": 0.2606, "median": 0.3178, "q3": 0.3706, "max": 0.6764, "iqr": 0.11, "lower_bound": 0.0955, "upper_bound": 0.5357, "lower_outlier_count": 9, "upper_outlier_count": 6, "insight": "3-gram重复度(反映文本短语重复程度和表达多样性)：3-gram重复度普遍超过正常范围，短语重复度过高，可能是模板文本；数据变化幅度适中，样本分布相对合理"}, "mtld_score": {"count": 41, "mean": 26.8031, "std": 4.9863, "min": 17.4286, "q1": 23.4444, "median": 26.6111, "q3": 28.8824, "max": 38.2308, "iqr": 5.4379, "lower_bound": 15.2876, "upper_bound": 37.0392, "lower_outlier_count": 0, "upper_outlier_count": 3, "insight": "MTLD词汇多样性(反映文本词汇丰富度和表达复杂性)：词汇多样性普遍不足，可能是重复文本或词汇表达有限"}}, "response_length": {"char_count": {"count": 50, "mean": 2.88, "std": 0.7386, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "字符数(反映样本信息密度和完整性)：有9个样本少于3字符；有7个样本超过3字符；字符数分布左偏，大部分样本较长，少数样本明显较短"}, "alpha_ratio": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "digit_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "数字占比(反映文本中数值信息的占比密度)：数字占比变化幅度很小，数值信息密度高度一致"}, "alnum_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "字母数字占比(反映文本中字母数字字符的总体占比)：数据变化幅度很小，样本差异不大"}, "valid_ratio": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "word_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "词数(反映样本信息量和内容丰富度)：词数变化幅度很小，样本信息量高度一致"}, "stopword_count": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "avg_word_length": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "sentence_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "句子数(反映样本结构完整性和语义连贯性)：数据变化幅度很小，样本差异不大"}, "line_count": {"count": 50, "mean": 1.0, "std": 0.0, "min": 1.0, "q1": 1.0, "median": 1.0, "q3": 1.0, "max": 1.0, "iqr": 0.0, "lower_bound": 1.0, "upper_bound": 1.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": "行数(反映文本格式结构和排版方式)：数据变化幅度很小，样本差异不大"}, "avg_sentence_length": {"count": 50, "mean": 2.88, "std": 0.7386, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "平均句子长度(反映句子结构复杂度和断句质量)：句子长度普遍低于正常范围，可能存在过度断句或句子结构简单"}, "avg_line_length": {"count": 50, "mean": 2.88, "std": 0.7386, "min": 1.0, "q1": 3.0, "median": 3.0, "q3": 3.0, "max": 4.0, "iqr": 0.0, "lower_bound": 3.0, "upper_bound": 3.0, "lower_outlier_count": 9, "upper_outlier_count": 7, "insight": "平均行长度(反映文本格式规范性和排版质量)：有9个样本平均行长度低于3.0字符；有7个样本平均行长度超过3.0字符；数据分布左偏，大部分样本值偏大，少数样本值较小"}, "bigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "trigram_repetition": {"count": 50, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}, "mtld_score": {"count": 0, "mean": 0.0, "std": 0.0, "min": 0.0, "q1": 0.0, "median": 0.0, "q3": 0.0, "max": 0.0, "iqr": 0.0, "lower_bound": 0.0, "upper_bound": 0.0, "lower_outlier_count": 0, "upper_outlier_count": 0, "insight": ""}}}, "anomaly_stats": {"system": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "system_length": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "prompt": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "prompt_length": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "response": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}, "response_length": {"special_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "non_standard_spaces_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "control_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}, "format_chars_ratio": {"anomaly_sample_proportion": 0.0, "median_anomaly_ratio": 0.0, "mean_anomaly_ratio": 0.0, "max_anomaly_ratio": 0.0, "min_anomaly_ratio": 0.0, "insight": ""}}}, "proportion_stats": {"language": {"total_count": 50, "proportions": {"zh": {"count": 50, "proportion": 1.0}}, "most_common": ["zh", 50], "insight": "language字段(总样本50，1个类别)存在以下问题：language字段分布过度集中于'zh'类别(100.00%)建议：增加数据来源多样性。"}, "encoding": {"total_count": 50, "proportions": {"utf-8": {"count": 50, "proportion": 1.0}}, "most_common": ["utf-8", 50], "insight": "encoding字段(总样本50，1个类别)存在以下问题：encoding字段分布过度集中于'utf-8'类别(100.00%)建议：增加数据来源多样性。"}}, "pattern_stats": {"system": {"pii_email": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["<EMAIL>", 8], ["<EMAIL>", 7], ["<EMAIL>", 5]], "insight": "邮箱地址匹配比例为32%，存在一定数量真实邮箱，匹配准确，数据质量较好，无明显误匹配。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 12, "proportion": 0.24, "most_common_matches": [["19900101123", 6], ["19771212345", 5], ["19850505234", 4]], "insight": "手机号码匹配比例24%，匹配样本较少但格式规范，真实数据较为可信，误匹配概率低。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 12, "proportion": 0.24, "most_common_matches": [["110101199001011234", 6], ["******************", 5], ["320102198505052345", 4]], "insight": "身份证号匹配比例24%，数量有限但格式严谨，真实身份证号匹配，数据质量正常。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配相关数据，无法评估，建议后续增加姓名识别以完善PII检测。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 7, "proportion": 0.14, "most_common_matches": [["<div>", 9], ["</div>", 9]], "insight": "HTML标签匹配比例14%，样本较少但匹配准确，存在少量HTML代码片段，数据质量良好。"}, "urls": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["demo.org", 8], ["example.com", 7], ["company.net", 4]], "insight": "网址链接匹配比例32%，匹配示例真实有效，匹配准确，无误匹配问题。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配比例100%，示例均为‘无’，说明敏感词规则可能未覆盖，存在规则不完善风险。"}}, "system_length": {"pii_email": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无邮箱地址匹配数据，表明该字段无相关PII识别，数据质量稳定但信息有限。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无手机号码匹配数据，表明该字段无相关PII识别，信息缺失。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无身份证号匹配数据，表明该字段无相关PII识别，数据缺失。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配数据，缺乏姓名识别，建议补充相关规则。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无HTML标签匹配数据，字段对应内容无HTML代码，数据质量正常。"}, "urls": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无网址链接匹配数据，无相关URL信息，数据完整性有待提升。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配100%，示例均为‘无’，敏感词检测规则可能存在覆盖不足问题。"}}, "prompt": {"pii_email": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["<EMAIL>", 10], ["<EMAIL>", 9], ["<EMAIL>", 6]], "insight": "邮箱匹配比例32%，匹配准确且示例真实，数据质量较好，无明显误匹配。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 21, "proportion": 0.42, "most_common_matches": [["19850505234", 18], ["19771212345", 11], ["19900101123", 4]], "insight": "手机号码匹配比例42%，较system字段更高，匹配真实有效，数据质量较优。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 21, "proportion": 0.42, "most_common_matches": [["320102198505052345", 18], ["******************", 11], ["110101199001011234", 4]], "insight": "身份证号匹配比例42%，匹配数量增加，格式规范，真实数据占比高。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配数据，仍缺少姓名识别，建议信息补充加强PII覆盖。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 6, "proportion": 0.12, "most_common_matches": [["<div>", 6], ["</div>", 6]], "insight": "HTML标签匹配比例12%，匹配数量少且准确，数据质量良好。"}, "urls": {"total_samples": 50, "samples_with_pattern": 16, "proportion": 0.32, "most_common_matches": [["demo.org", 10], ["example.com", 9], ["company.net", 6]], "insight": "网址链接匹配32%，匹配准确，示例真实有效，无误匹配。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配100%，示例均为‘无’，敏感词规则覆盖面不足，存在改进空间。"}}, "prompt_length": {"pii_email": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无邮箱匹配数据，字段缺乏PII邮箱识别，信息不足。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无手机号码匹配数据，缺少相关识别，数据完整性待提升。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无身份证匹配数据，缺少识别，建议后续完善。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配数据，姓名识别缺失，建议补充。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无HTML标签匹配，显示字段对应文本无HTML代码，数据质量正常。"}, "urls": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无网址链接匹配，字段无链接信息，信息缺失。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配100%，示例均为‘无’，敏感词检测规则应加强覆盖。"}}, "response": {"pii_email": {"total_samples": 50, "samples_with_pattern": 24, "proportion": 0.48, "most_common_matches": [["<EMAIL>", 14], ["<EMAIL>", 13], ["<EMAIL>", 12]], "insight": "邮箱匹配比例48%，为所有字段中最高，匹配准确且数量充足，数据质量优良。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 27, "proportion": 0.54, "most_common_matches": [["19850505234", 20], ["19771212345", 16], ["19900101123", 13]], "insight": "手机号码匹配比例54%，最高匹配比例，匹配真实有效，数据质量较好。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 27, "proportion": 0.54, "most_common_matches": [["320102198505052345", 20], ["******************", 16], ["110101199001011234", 13]], "insight": "身份证号匹配比例54%，最高匹配数量且格式正确，数据真实可信。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配数据，PII姓名识别缺失，建议增强该部分识别能力。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 18, "proportion": 0.36, "most_common_matches": [["<div>", 21], ["</div>", 21]], "insight": "HTML标签匹配比例36%，最高比例，可能因response含更多格式化内容，匹配准确无误。"}, "urls": {"total_samples": 50, "samples_with_pattern": 24, "proportion": 0.48, "most_common_matches": [["example.com", 14], ["company.net", 13], ["demo.org", 12]], "insight": "网址链接匹配比例48%，匹配准确且示例真实，数据质量较好。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配100%，示例均为‘无’，敏感词检测规则覆盖不足，需优化。"}}, "response_length": {"pii_email": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无邮箱匹配数据，缺少该类PII识别，数据完整性不足。"}, "pii_phone": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无手机号码匹配数据，缺少识别，建议补充。"}, "pii_id_card": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无身份证号匹配，信息缺失，需完善识别规则。"}, "pii_name": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无姓名匹配，缺乏相关识别，建议优化。"}, "html_tags": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无HTML标签匹配，字段对应文本无HTML代码，数据质量正常。"}, "urls": {"total_samples": 50, "samples_with_pattern": 0, "proportion": 0.0, "most_common_matches": [], "insight": "无网址链接匹配，字段无URL信息，数据缺失。"}, "sensitive_words": {"total_samples": 50, "samples_with_pattern": 50, "proportion": 1.0, "by_type": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "by_level": {"": {"word_count": 50, "sample_count": 50, "sample_proportion": 1.0}}, "unique_words": 1, "most_common_words": [["", 50]], "insight": "敏感词汇匹配100%，示例均为‘无’，敏感词规则覆盖仍需加强。"}}}, "insight_metadata": {"analysis_timestamp": "2025-07-25T03:44:27.024253", "analyzer_version": "2.0.0", "insights_embedded": true}}